<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - E-fulfilment en Distributie" id="id-logistix-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote klant" id="id-actor-grote-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Klein<PERSON> klant" id="id-actor-kleine-klant"/>
    <element xsi:type="archimate:BusinessService" name="E-fulfilment dienstverlening" id="id-service-efulfilment"/>
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-process-order-ontvangen"/>
    <element xsi:type="archimate:BusinessProcess" name="Order invoeren" id="id-process-order-invoeren"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad check" id="id-process-voorraad-check"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking &amp; Tracking" id="id-process-picking-tracking"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzendlabel maken" id="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:BusinessProcess" name="Tracking kopiëren" id="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantcommunicatie" id="id-process-klantcommunicatie"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationComponent" name="ERP (cloud)" id="id-app-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (on-premise)" id="id-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (standalone)" id="id-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM (losstaand)" id="id-app-crm"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud platform" id="id-node-cloud"/>
    <element xsi:type="archimate:Node" name="On-premise server" id="id-node-onpremise"/>
    <element xsi:type="archimate:Node" name="Standalone node TMS" id="id-node-standalone-tms"/>
    <element xsi:type="archimate:Node" name="Standalone node CRM" id="id-node-standalone-crm"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-grote-klant-order" source="id-actor-grote-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-kleine-klant-order" source="id-actor-kleine-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-order-ontvangen-invoeren" source="id-process-order-ontvangen" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-order-invoeren-voorraad" source="id-process-order-invoeren" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-voorraad-picking" source="id-process-voorraad-check" target="id-process-picking-tracking"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-picking-verzendlabel" source="id-process-picking-tracking" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-verzendlabel-tracking" source="id-process-verzendlabel-maken" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-tracking-klantcommunicatie" source="id-process-tracking-kopieren" target="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-erp-order-invoeren" source="id-app-erp" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-wms-voorraad-check" source="id-app-wms" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-wms-picking-tracking" source="id-app-wms" target="id-process-picking-tracking"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-tms-verzendlabel" source="id-app-tms" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-tracking" source="id-app-tms" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-crm-klantcommunicatie" source="id-app-crm" target="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-process-service" source="id-process-order-ontvangen" target="id-service-efulfilment"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-cloud-erp" source="id-node-cloud" target="id-app-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-wms" source="id-node-onpremise" target="id-app-wms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-standalone-tms" source="id-node-standalone-tms" target="id-app-tms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-standalone-crm" source="id-node-standalone-crm" target="id-app-crm"/>
  </folder>
  <folder name="Views" id="id-views-folder" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix BV - Huidige Situatie" id="id-view-current-state">
      <child xsi:type="archimate:DiagramObject" id="id-diagram-grote-klant" archimateElement="id-actor-grote-klant">
        <bounds x="100" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-grote-klant-order" source="id-diagram-grote-klant" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-grote-klant-order"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-kleine-klant" archimateElement="id-actor-kleine-klant">
        <bounds x="300" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-kleine-klant-order" source="id-diagram-kleine-klant" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-kleine-klant-order"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-order-ontvangen" targetConnections="id-conn-grote-klant-order id-conn-kleine-klant-order" archimateElement="id-process-order-ontvangen">
        <bounds x="200" y="150" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-order-ontvangen-invoeren" source="id-diagram-order-ontvangen" target="id-diagram-order-invoeren" archimateRelationship="id-rel-order-ontvangen-invoeren"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-process-service" source="id-diagram-order-ontvangen" target="id-diagram-service-efulfilment" archimateRelationship="id-rel-process-service"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-order-invoeren" targetConnections="id-conn-order-ontvangen-invoeren id-conn-erp-order-invoeren" archimateElement="id-process-order-invoeren">
        <bounds x="200" y="230" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-order-invoeren-voorraad" source="id-diagram-order-invoeren" target="id-diagram-voorraad-check" archimateRelationship="id-rel-order-invoeren-voorraad"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-voorraad-check" targetConnections="id-conn-order-invoeren-voorraad id-conn-wms-voorraad-check" archimateElement="id-process-voorraad-check">
        <bounds x="200" y="310" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-voorraad-picking" source="id-diagram-voorraad-check" target="id-diagram-picking-tracking" archimateRelationship="id-rel-voorraad-picking"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-picking-tracking" targetConnections="id-conn-voorraad-picking id-conn-wms-picking-tracking" archimateElement="id-process-picking-tracking">
        <bounds x="200" y="390" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-picking-verzendlabel" source="id-diagram-picking-tracking" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-picking-verzendlabel"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-verzendlabel-maken" targetConnections="id-conn-picking-verzendlabel id-conn-tms-verzendlabel" archimateElement="id-process-verzendlabel-maken">
        <bounds x="200" y="470" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-verzendlabel-tracking" source="id-diagram-verzendlabel-maken" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-verzendlabel-tracking"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-tracking-kopieren" targetConnections="id-conn-verzendlabel-tracking id-conn-tms-tracking" archimateElement="id-process-tracking-kopieren">
        <bounds x="200" y="550" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tracking-klantcommunicatie" source="id-diagram-tracking-kopieren" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-tracking-klantcommunicatie"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-klantcommunicatie" targetConnections="id-conn-tracking-klantcommunicatie id-conn-crm-klantcommunicatie" archimateElement="id-process-klantcommunicatie">
        <bounds x="200" y="630" width="120" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-service-efulfilment" targetConnections="id-conn-process-service" archimateElement="id-service-efulfilment">
        <bounds x="450" y="150" width="168" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-erp" archimateElement="id-app-erp">
        <bounds x="400" y="230" width="150" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-erp-order-invoeren" source="id-diagram-erp" target="id-diagram-order-invoeren" archimateRelationship="id-rel-erp-order-invoeren"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-wms" archimateElement="id-app-wms">
        <bounds x="400" y="350" width="150" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-voorraad-check" source="id-diagram-wms" target="id-diagram-voorraad-check" archimateRelationship="id-rel-wms-voorraad-check"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-wms-picking-tracking" source="id-diagram-wms" target="id-diagram-picking-tracking" archimateRelationship="id-rel-wms-picking-tracking"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-tms" archimateElement="id-app-tms">
        <bounds x="400" y="510" width="150" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-verzendlabel" source="id-diagram-tms" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-tms-verzendlabel"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-tracking" source="id-diagram-tms" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-tms-tracking"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-crm" archimateElement="id-app-crm">
        <bounds x="400" y="630" width="150" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-crm-klantcommunicatie" source="id-diagram-crm" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-crm-klantcommunicatie"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-cloud" archimateElement="id-node-cloud">
        <bounds x="350" y="750" width="150" height="49"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-cloud-erp" source="id-diagram-cloud" target="id-diagram-erp" archimateRelationship="id-rel-cloud-erp"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-onpremise" archimateElement="id-node-onpremise">
        <bounds x="350" y="820" width="150" height="49"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-onpremise-wms" source="id-diagram-onpremise" target="id-diagram-wms" archimateRelationship="id-rel-onpremise-wms"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-standalone-tms" archimateElement="id-node-standalone-tms">
        <bounds x="350" y="890" width="150" height="49"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-standalone-tms" source="id-diagram-standalone-tms" target="id-diagram-tms" archimateRelationship="id-rel-standalone-tms"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-standalone-crm" archimateElement="id-node-standalone-crm">
        <bounds x="350" y="960" width="150" height="49"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-standalone-crm" source="id-diagram-standalone-crm" target="id-diagram-crm" archimateRelationship="id-rel-standalone-crm"/>
      </child>
    </element>
  </folder>
</archimate:model>
