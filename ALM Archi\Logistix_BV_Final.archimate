<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix BV - E-fulfilment en Distributie" id="id-logistix-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Grote klant" id="id-actor-grote-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Kleine klant" id="id-actor-kleine-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Medewerker" id="id-actor-medewerker"/>
    <element xsi:type="archimate:BusinessActor" name="Klantenservice" id="id-actor-klantenservice"/>
    <element xsi:type="archimate:BusinessActor" name="Vervoerder" id="id-actor-vervoerder"/>
    <element xsi:type="archimate:BusinessService" name="E-fulfilment dienstverlening" id="id-service-efulfilment"/>
    <element xsi:type="archimate:BusinessService" name="Opslag &amp; Distributie" id="id-service-opslag-distributie"/>
    <element xsi:type="archimate:BusinessProcess" name="Orderverwerking" id="id-process-orderverwerking"/>
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-process-order-ontvangen"/>
    <element xsi:type="archimate:BusinessProcess" name="Order handmatig invoeren" id="id-process-order-invoeren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantgegevens controleren" id="id-process-klantgegevens-check"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad valideren" id="id-process-voorraad-check"/>
    <element xsi:type="archimate:BusinessProcess" name="Magazijnbeheer" id="id-process-magazijnbeheer"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking &amp; Packing" id="id-process-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzendlabel handmatig maken" id="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:BusinessProcess" name="Tracking handmatig kopiëren" id="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantcommunicatie" id="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:BusinessProcess" name="Status opzoeken" id="id-process-status-opzoeken"/>
    <element xsi:type="archimate:BusinessObject" name="Order" id="id-object-order"/>
    <element xsi:type="archimate:BusinessObject" name="Klantgegevens" id="id-object-klantgegevens"/>
    <element xsi:type="archimate:BusinessObject" name="Voorraadgegevens" id="id-object-voorraadgegevens"/>
    <element xsi:type="archimate:BusinessObject" name="Picklijst" id="id-object-picklijst"/>
    <element xsi:type="archimate:BusinessObject" name="Verzendlabel" id="id-object-verzendlabel"/>
    <element xsi:type="archimate:BusinessObject" name="Trackingnummer" id="id-object-trackingnummer"/>
    <element xsi:type="archimate:BusinessRole" name="Magazijnmedewerker" id="id-role-magazijnmedewerker"/>
    <element xsi:type="archimate:BusinessRole" name="Orderverwerker" id="id-role-orderverwerker"/>
    <element xsi:type="archimate:BusinessRole" name="Klantenservice medewerker" id="id-role-klantenservice-medewerker"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationComponent" name="ERP Systeem (cloud)" id="id-app-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS (on-premise)" id="id-app-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS (standalone)" id="id-app-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM (losstaand)" id="id-app-crm"/>
    <element xsi:type="archimate:ApplicationService" name="API-koppeling" id="id-service-api"/>
    <element xsi:type="archimate:ApplicationService" name="E-mail interface" id="id-service-email"/>
    <element xsi:type="archimate:ApplicationService" name="Batchbestand verwerking" id="id-service-batch"/>
    <element xsi:type="archimate:ApplicationService" name="Klantgegevens beheer" id="id-service-klantbeheer"/>
    <element xsi:type="archimate:ApplicationService" name="Voorraad beheer" id="id-service-voorraad"/>
    <element xsi:type="archimate:ApplicationService" name="Picklijst generatie" id="id-service-picklijst"/>
    <element xsi:type="archimate:ApplicationService" name="Verzendlabel service" id="id-service-verzendlabel"/>
    <element xsi:type="archimate:ApplicationService" name="Tracking service" id="id-service-tracking"/>
    <element xsi:type="archimate:ApplicationService" name="Klantcontact beheer" id="id-service-klantcontact"/>
    <element xsi:type="archimate:DataObject" name="Orderdata" id="id-data-order"/>
    <element xsi:type="archimate:DataObject" name="Klantdata" id="id-data-klant"/>
    <element xsi:type="archimate:DataObject" name="Voorraaddata" id="id-data-voorraad"/>
    <element xsi:type="archimate:DataObject" name="Verzenddata" id="id-data-verzend"/>
    <element xsi:type="archimate:DataObject" name="Trackingdata" id="id-data-tracking"/>
    <element xsi:type="archimate:DataObject" name="Klantcontactdata" id="id-data-klantcontact"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud platform" id="id-node-cloud"/>
    <element xsi:type="archimate:Node" name="On-premise server" id="id-node-onpremise"/>
    <element xsi:type="archimate:Node" name="TMS Server" id="id-node-standalone-tms"/>
    <element xsi:type="archimate:Node" name="CRM Server" id="id-node-standalone-crm"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk" id="id-network"/>
    <element xsi:type="archimate:Device" name="Werkstation medewerker" id="id-device-werkstation"/>
    <element xsi:type="archimate:Device" name="Magazijn terminal" id="id-device-magazijn"/>
    <element xsi:type="archimate:TechnologyService" name="Cloud hosting" id="id-tech-service-cloud"/>
    <element xsi:type="archimate:TechnologyService" name="On-premise hosting" id="id-tech-service-onpremise"/>
    <element xsi:type="archimate:TechnologyService" name="Netwerkconnectiviteit" id="id-tech-service-network"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <!-- Business Actor Relations -->
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-grote-klant-order" source="id-actor-grote-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-kleine-klant-order" source="id-actor-kleine-klant" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-medewerker-orderverwerker" source="id-actor-medewerker" target="id-role-orderverwerker"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-medewerker-magazijn" source="id-actor-medewerker" target="id-role-magazijnmedewerker"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-klantenservice-role" source="id-actor-klantenservice" target="id-role-klantenservice-medewerker"/>

    <!-- Business Process Relations -->
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-orderverwerking-ontvangen" source="id-process-orderverwerking" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-orderverwerking-invoeren" source="id-process-orderverwerking" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-orderverwerking-klantcheck" source="id-process-orderverwerking" target="id-process-klantgegevens-check"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-magazijnbeheer-voorraad" source="id-process-magazijnbeheer" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-magazijnbeheer-picking" source="id-process-magazijnbeheer" target="id-process-picking-packing"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-magazijnbeheer-verzendlabel" source="id-process-magazijnbeheer" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-magazijnbeheer-tracking" source="id-process-magazijnbeheer" target="id-process-tracking-kopieren"/>

    <!-- Process Flow Relations -->
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-order-ontvangen-invoeren" source="id-process-order-ontvangen" target="id-process-order-invoeren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-order-invoeren-klantcheck" source="id-process-order-invoeren" target="id-process-klantgegevens-check"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-klantcheck-voorraad" source="id-process-klantgegevens-check" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-voorraad-picking" source="id-process-voorraad-check" target="id-process-picking-packing"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-picking-verzendlabel" source="id-process-picking-packing" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-verzendlabel-tracking" source="id-process-verzendlabel-maken" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:TriggeringRelationship" id="id-rel-tracking-klantcommunicatie" source="id-process-tracking-kopieren" target="id-process-klantcommunicatie"/>

    <!-- Role to Process Relations -->
    <element xsi:type="archimate:ServingRelationship" id="id-rel-orderverwerker-orderverwerking" source="id-role-orderverwerker" target="id-process-orderverwerking"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-magazijnmedewerker-magazijnbeheer" source="id-role-magazijnmedewerker" target="id-process-magazijnbeheer"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantenservice-communicatie" source="id-role-klantenservice-medewerker" target="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantenservice-status" source="id-role-klantenservice-medewerker" target="id-process-status-opzoeken"/>

    <!-- Application Service Relations -->
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-erp-api" source="id-app-erp" target="id-service-api"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-erp-email" source="id-app-erp" target="id-service-email"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-erp-batch" source="id-app-erp" target="id-service-batch"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-erp-klantbeheer" source="id-app-erp" target="id-service-klantbeheer"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-wms-voorraad" source="id-app-wms" target="id-service-voorraad"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-wms-picklijst" source="id-app-wms" target="id-service-picklijst"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-tms-verzendlabel" source="id-app-tms" target="id-service-verzendlabel"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-tms-tracking" source="id-app-tms" target="id-service-tracking"/>
    <element xsi:type="archimate:CompositionRelationship" id="id-rel-crm-klantcontact" source="id-app-crm" target="id-service-klantcontact"/>

    <!-- Application to Process Relations -->
    <element xsi:type="archimate:ServingRelationship" id="id-rel-api-order-ontvangen" source="id-service-api" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-email-order-ontvangen" source="id-service-email" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-batch-order-ontvangen" source="id-service-batch" target="id-process-order-ontvangen"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantbeheer-klantcheck" source="id-service-klantbeheer" target="id-process-klantgegevens-check"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-voorraad-voorraadcheck" source="id-service-voorraad" target="id-process-voorraad-check"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-picklijst-picking" source="id-service-picklijst" target="id-process-picking-packing"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-verzendlabel-verzendlabel" source="id-service-verzendlabel" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-tracking-tracking" source="id-service-tracking" target="id-process-tracking-kopieren"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantcontact-communicatie" source="id-service-klantcontact" target="id-process-klantcommunicatie"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-klantcontact-status" source="id-service-klantcontact" target="id-process-status-opzoeken"/>

    <!-- Data Access Relations -->
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-orderdata" source="id-app-erp" target="id-data-order"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-erp-klantdata" source="id-app-erp" target="id-data-klant"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-wms-voorraaddata" source="id-app-wms" target="id-data-voorraad"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-verzenddata" source="id-app-tms" target="id-data-verzend"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tms-trackingdata" source="id-app-tms" target="id-data-tracking"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-crm-klantcontactdata" source="id-app-crm" target="id-data-klantcontact"/>

    <!-- Business Object Relations -->
    <element xsi:type="archimate:AccessRelationship" id="id-rel-order-ontvangen-order" source="id-process-order-ontvangen" target="id-object-order"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-klantcheck-klantgegevens" source="id-process-klantgegevens-check" target="id-object-klantgegevens"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-voorraadcheck-voorraad" source="id-process-voorraad-check" target="id-object-voorraadgegevens"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-picking-picklijst" source="id-process-picking-packing" target="id-object-picklijst"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-verzendlabel-label" source="id-process-verzendlabel-maken" target="id-object-verzendlabel"/>
    <element xsi:type="archimate:AccessRelationship" id="id-rel-tracking-nummer" source="id-process-tracking-kopieren" target="id-object-trackingnummer"/>

    <!-- Service Realization Relations -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-orderverwerking-efulfilment" source="id-process-orderverwerking" target="id-service-efulfilment"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-magazijnbeheer-opslag" source="id-process-magazijnbeheer" target="id-service-opslag-distributie"/>

    <!-- Technology Relations -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-cloud-erp" source="id-node-cloud" target="id-app-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-wms" source="id-node-onpremise" target="id-app-wms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-tms-server-tms" source="id-node-standalone-tms" target="id-app-tms"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-crm-server-crm" source="id-node-standalone-crm" target="id-app-crm"/>

    <!-- Technology Service Relations -->
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-cloud-service" source="id-node-cloud" target="id-tech-service-cloud"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-onpremise-service" source="id-node-onpremise" target="id-tech-service-onpremise"/>
    <element xsi:type="archimate:RealizationRelationship" id="id-rel-network-service" source="id-network" target="id-tech-service-network"/>

    <!-- Device Relations -->
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-werkstation-orderverwerker" source="id-device-werkstation" target="id-role-orderverwerker"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-werkstation-klantenservice" source="id-device-werkstation" target="id-role-klantenservice-medewerker"/>
    <element xsi:type="archimate:AssignmentRelationship" id="id-rel-magazijn-terminal-magazijn" source="id-device-magazijn" target="id-role-magazijnmedewerker"/>

    <!-- Network Relations -->
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-cloud" source="id-network" target="id-node-cloud"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-onpremise" source="id-network" target="id-node-onpremise"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-tms" source="id-network" target="id-node-standalone-tms"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-crm" source="id-network" target="id-node-standalone-crm"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-werkstation" source="id-network" target="id-device-werkstation"/>
    <element xsi:type="archimate:AssociationRelationship" id="id-rel-network-magazijn" source="id-network" target="id-device-magazijn"/>

    <!-- External Actor Relations -->
    <element xsi:type="archimate:ServingRelationship" id="id-rel-vervoerder-verzendlabel" source="id-actor-vervoerder" target="id-process-verzendlabel-maken"/>
    <element xsi:type="archimate:ServingRelationship" id="id-rel-vervoerder-tracking" source="id-actor-vervoerder" target="id-process-tracking-kopieren"/>
  </folder>
  <folder name="Views" id="id-views-folder" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix BV - Huidige Situatie Overzicht" id="id-view-current-state">
      <!-- Business Actors -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-grote-klant" archimateElement="id-actor-grote-klant">
        <bounds x="50" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-grote-klant-order" source="id-diagram-grote-klant" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-grote-klant-order"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-kleine-klant" archimateElement="id-actor-kleine-klant">
        <bounds x="200" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-kleine-klant-order" source="id-diagram-kleine-klant" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-kleine-klant-order"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-klantenservice" archimateElement="id-actor-klantenservice">
        <bounds x="350" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-klantenservice-role" source="id-diagram-klantenservice" target="id-diagram-klantenservice-role" archimateRelationship="id-rel-klantenservice-role"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-vervoerder" archimateElement="id-actor-vervoerder">
        <bounds x="500" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-vervoerder-verzendlabel" source="id-diagram-vervoerder" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-vervoerder-verzendlabel"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-vervoerder-tracking" source="id-diagram-vervoerder" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-vervoerder-tracking"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-medewerker" archimateElement="id-actor-medewerker">
        <bounds x="650" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-medewerker-orderverwerker" source="id-diagram-medewerker" target="id-diagram-orderverwerker-role" archimateRelationship="id-rel-medewerker-orderverwerker"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-medewerker-magazijn" source="id-diagram-medewerker" target="id-diagram-magazijnmedewerker-role" archimateRelationship="id-rel-medewerker-magazijn"/>
      </child>

      <!-- Business Services -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-service-efulfilment" targetConnections="id-conn-orderverwerking-efulfilment" archimateElement="id-service-efulfilment">
        <bounds x="700" y="150" width="168" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-service-opslag" targetConnections="id-conn-magazijnbeheer-opslag" archimateElement="id-service-opslag-distributie">
        <bounds x="700" y="250" width="168" height="55"/>
      </child>

      <!-- Business Roles -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-orderverwerker-role" targetConnections="id-conn-medewerker-orderverwerker" archimateElement="id-role-orderverwerker">
        <bounds x="50" y="150" width="133" height="44"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-orderverwerker-orderverwerking" source="id-diagram-orderverwerker-role" target="id-diagram-orderverwerking" archimateRelationship="id-rel-orderverwerker-orderverwerking"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-magazijnmedewerker-role" targetConnections="id-conn-medewerker-magazijn" archimateElement="id-role-magazijnmedewerker">
        <bounds x="50" y="250" width="133" height="44"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-magazijnmedewerker-magazijnbeheer" source="id-diagram-magazijnmedewerker-role" target="id-diagram-magazijnbeheer" archimateRelationship="id-rel-magazijnmedewerker-magazijnbeheer"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-klantenservice-role" targetConnections="id-conn-klantenservice-role" archimateElement="id-role-klantenservice-medewerker">
        <bounds x="350" y="150" width="133" height="44"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-klantenservice-communicatie" source="id-diagram-klantenservice-role" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-klantenservice-communicatie"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-klantenservice-status" source="id-diagram-klantenservice-role" target="id-diagram-status-opzoeken" archimateRelationship="id-rel-klantenservice-status"/>
      </child>

      <!-- Main Business Processes -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-orderverwerking" targetConnections="id-conn-orderverwerker-orderverwerking" archimateElement="id-process-orderverwerking">
        <bounds x="200" y="150" width="480" height="85"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-orderverwerking-efulfilment" source="id-diagram-orderverwerking" target="id-diagram-service-efulfilment" archimateRelationship="id-rel-orderverwerking-efulfilment"/>

        <!-- Sub-processes within Orderverwerking -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-order-ontvangen" targetConnections="id-conn-grote-klant-order id-conn-kleine-klant-order id-conn-orderverwerking-ontvangen" archimateElement="id-process-order-ontvangen">
          <bounds x="24" y="24" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-order-ontvangen-invoeren" source="id-diagram-order-ontvangen" target="id-diagram-order-invoeren" archimateRelationship="id-rel-order-ontvangen-invoeren"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-order-invoeren" targetConnections="id-conn-order-ontvangen-invoeren id-conn-orderverwerking-invoeren" archimateElement="id-process-order-invoeren">
          <bounds x="156" y="24" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-order-invoeren-klantcheck" source="id-diagram-order-invoeren" target="id-diagram-klantgegevens-check" archimateRelationship="id-rel-order-invoeren-klantcheck"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-klantgegevens-check" targetConnections="id-conn-order-invoeren-klantcheck id-conn-orderverwerking-klantcheck" archimateElement="id-process-klantgegevens-check">
          <bounds x="288" y="24" width="120" height="55"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-magazijnbeheer" targetConnections="id-conn-magazijnmedewerker-magazijnbeheer" archimateElement="id-process-magazijnbeheer">
        <bounds x="200" y="250" width="480" height="165"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-magazijnbeheer-opslag" source="id-diagram-magazijnbeheer" target="id-diagram-service-opslag" archimateRelationship="id-rel-magazijnbeheer-opslag"/>

        <!-- Sub-processes within Magazijnbeheer -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-voorraad-check" targetConnections="id-conn-klantcheck-voorraad id-conn-magazijnbeheer-voorraad" archimateElement="id-process-voorraad-check">
          <bounds x="24" y="24" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-voorraad-picking" source="id-diagram-voorraad-check" target="id-diagram-picking-packing" archimateRelationship="id-rel-voorraad-picking"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-picking-packing" targetConnections="id-conn-voorraad-picking id-conn-magazijnbeheer-picking" archimateElement="id-process-picking-packing">
          <bounds x="156" y="24" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-picking-verzendlabel" source="id-diagram-picking-packing" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-picking-verzendlabel"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-verzendlabel-maken" targetConnections="id-conn-picking-verzendlabel id-conn-vervoerder-verzendlabel id-conn-magazijnbeheer-verzendlabel" archimateElement="id-process-verzendlabel-maken">
          <bounds x="288" y="24" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-verzendlabel-tracking" source="id-diagram-verzendlabel-maken" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-verzendlabel-tracking"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-tracking-kopieren" targetConnections="id-conn-verzendlabel-tracking id-conn-vervoerder-tracking id-conn-magazijnbeheer-tracking" archimateElement="id-process-tracking-kopieren">
          <bounds x="24" y="96" width="120" height="55"/>
          <sourceConnection xsi:type="archimate:Connection" id="id-conn-tracking-klantcommunicatie" source="id-diagram-tracking-kopieren" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-tracking-klantcommunicatie"/>
        </child>
      </child>

      <!-- Additional Business Processes -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-klantcommunicatie" targetConnections="id-conn-tracking-klantcommunicatie id-conn-klantenservice-communicatie" archimateElement="id-process-klantcommunicatie">
        <bounds x="200" y="450" width="120" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="id-diagram-status-opzoeken" targetConnections="id-conn-klantenservice-status" archimateElement="id-process-status-opzoeken">
        <bounds x="350" y="450" width="120" height="55"/>
      </child>

      <!-- Application Layer -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-erp" archimateElement="id-app-erp">
        <bounds x="50" y="550" width="200" height="120"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-api-order-ontvangen" source="id-diagram-erp" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-api-order-ontvangen"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-klantbeheer-klantcheck" source="id-diagram-erp" target="id-diagram-klantgegevens-check" archimateRelationship="id-rel-klantbeheer-klantcheck"/>

        <!-- ERP Services -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-api-service" archimateElement="id-service-api">
          <bounds x="12" y="24" width="80" height="35"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-email-service" archimateElement="id-service-email">
          <bounds x="104" y="24" width="80" height="35"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-batch-service" archimateElement="id-service-batch">
          <bounds x="12" y="72" width="80" height="35"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-klantbeheer-service" archimateElement="id-service-klantbeheer">
          <bounds x="104" y="72" width="80" height="35"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-wms" archimateElement="id-app-wms">
        <bounds x="280" y="550" width="200" height="120"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-voorraad-voorraadcheck" source="id-diagram-wms" target="id-diagram-voorraad-check" archimateRelationship="id-rel-voorraad-voorraadcheck"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-picklijst-picking" source="id-diagram-wms" target="id-diagram-picking-packing" archimateRelationship="id-rel-picklijst-picking"/>

        <!-- WMS Services -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-voorraad-service" archimateElement="id-service-voorraad">
          <bounds x="12" y="24" width="80" height="35"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-picklijst-service" archimateElement="id-service-picklijst">
          <bounds x="104" y="24" width="80" height="35"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-tms" archimateElement="id-app-tms">
        <bounds x="510" y="550" width="200" height="120"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-verzendlabel-verzendlabel" source="id-diagram-tms" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-verzendlabel-verzendlabel"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tracking-tracking" source="id-diagram-tms" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-tracking-tracking"/>

        <!-- TMS Services -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-verzendlabel-service" archimateElement="id-service-verzendlabel">
          <bounds x="12" y="24" width="80" height="35"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="id-diagram-tracking-service" archimateElement="id-service-tracking">
          <bounds x="104" y="24" width="80" height="35"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-crm" archimateElement="id-app-crm">
        <bounds x="740" y="550" width="200" height="120"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-klantcontact-communicatie" source="id-diagram-crm" target="id-diagram-klantcommunicatie" archimateRelationship="id-rel-klantcontact-communicatie"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-klantcontact-status" source="id-diagram-crm" target="id-diagram-status-opzoeken" archimateRelationship="id-rel-klantcontact-status"/>

        <!-- CRM Services -->
        <child xsi:type="archimate:DiagramObject" id="id-diagram-klantcontact-service" archimateElement="id-service-klantcontact">
          <bounds x="60" y="24" width="80" height="35"/>
        </child>
      </child>

      <!-- Technology Layer -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-cloud" archimateElement="id-node-cloud">
        <bounds x="50" y="720" width="200" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-cloud-erp" source="id-diagram-cloud" target="id-diagram-erp" archimateRelationship="id-rel-cloud-erp"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-onpremise" archimateElement="id-node-onpremise">
        <bounds x="280" y="720" width="200" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-onpremise-wms" source="id-diagram-onpremise" target="id-diagram-wms" archimateRelationship="id-rel-onpremise-wms"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-tms-server" archimateElement="id-node-standalone-tms">
        <bounds x="510" y="720" width="200" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-tms-server-tms" source="id-diagram-tms-server" target="id-diagram-tms" archimateRelationship="id-rel-tms-server-tms"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-crm-server" archimateElement="id-node-standalone-crm">
        <bounds x="740" y="720" width="200" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-crm-server-crm" source="id-diagram-crm-server" target="id-diagram-crm" archimateRelationship="id-rel-crm-server-crm"/>
      </child>

      <!-- Network -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-network" archimateElement="id-network">
        <bounds x="50" y="820" width="890" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-network-cloud" source="id-diagram-network" target="id-diagram-cloud" archimateRelationship="id-rel-network-cloud"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-network-onpremise" source="id-diagram-network" target="id-diagram-onpremise" archimateRelationship="id-rel-network-onpremise"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-network-tms" source="id-diagram-network" target="id-diagram-tms-server" archimateRelationship="id-rel-network-tms"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-network-crm" source="id-diagram-network" target="id-diagram-crm-server" archimateRelationship="id-rel-network-crm"/>
      </child>

      <!-- Devices -->
      <child xsi:type="archimate:DiagramObject" id="id-diagram-werkstation" archimateElement="id-device-werkstation">
        <bounds x="200" y="900" width="150" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-werkstation-orderverwerker" source="id-diagram-werkstation" target="id-diagram-orderverwerker-role" archimateRelationship="id-rel-werkstation-orderverwerker"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-werkstation-klantenservice" source="id-diagram-werkstation" target="id-diagram-klantenservice-role" archimateRelationship="id-rel-werkstation-klantenservice"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-network-werkstation" source="id-diagram-network" target="id-diagram-werkstation" archimateRelationship="id-rel-network-werkstation"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="id-diagram-magazijn-terminal" archimateElement="id-device-magazijn">
        <bounds x="400" y="900" width="150" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-magazijn-terminal-magazijn" source="id-diagram-magazijn-terminal" target="id-diagram-magazijnmedewerker-role" archimateRelationship="id-rel-magazijn-terminal-magazijn"/>
        <sourceConnection xsi:type="archimate:Connection" id="id-conn-network-magazijn" source="id-diagram-network" target="id-diagram-magazijn-terminal" archimateRelationship="id-rel-network-magazijn"/>
      </child>

      <!-- Missing connections for composition relationships -->
      <sourceConnection xsi:type="archimate:Connection" id="id-conn-orderverwerking-ontvangen" source="id-diagram-orderverwerking" target="id-diagram-order-ontvangen" archimateRelationship="id-rel-orderverwerking-ontvangen"/>
      <sourceConnection xsi:type="archimate:Connection" id="id-conn-orderverwerking-invoeren" source="id-diagram-orderverwerking" target="id-diagram-order-invoeren" archimateRelationship="id-rel-orderverwerking-invoeren"/>
      <sourceConnection xsi:type="archimate:Connection" id="id-conn-orderverwerking-klantcheck" source="id-diagram-orderverwerking" target="id-diagram-klantgegevens-check" archimateRelationship="id-rel-orderverwerking-klantcheck"/>
      <sourceConnection xsi:type="archimate:Connection" id="id-conn-magazijnbeheer-voorraad" source="id-diagram-magazijnbeheer" target="id-diagram-voorraad-check" archimateRelationship="id-rel-magazijnbeheer-voorraad"/>
      <sourceConnection xsi:type="archimate:Connection" id="id-conn-magazijnbeheer-picking" source="id-diagram-magazijnbeheer" target="id-diagram-picking-packing" archimateRelationship="id-rel-magazijnbeheer-picking"/>
      <sourceConnection xsi:type="archimate:Connection" id="id-conn-magazijnbeheer-verzendlabel" source="id-diagram-magazijnbeheer" target="id-diagram-verzendlabel-maken" archimateRelationship="id-rel-magazijnbeheer-verzendlabel"/>
      <sourceConnection xsi:type="archimate:Connection" id="id-conn-magazijnbeheer-tracking" source="id-diagram-magazijnbeheer" target="id-diagram-tracking-kopieren" archimateRelationship="id-rel-magazijnbeheer-tracking"/>
      <sourceConnection xsi:type="archimate:Connection" id="id-conn-klantcheck-voorraad" source="id-diagram-klantgegevens-check" target="id-diagram-voorraad-check" archimateRelationship="id-rel-klantcheck-voorraad"/>
    </element>
  </folder>
</archimate:model>
