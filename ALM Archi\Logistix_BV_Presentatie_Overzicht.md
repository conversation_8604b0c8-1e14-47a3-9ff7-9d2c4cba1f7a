# Logistix B.V. - ArchiMate Model Presentatie

## Slide 1: Titel
**Logistix B.V. - Enterprise Architectuur Analyse**
*E-fulfilment en Distributie Optimalisatie*

---

## Slide 2: Bedrijfsprofiel
**Logistix B.V.**
- Middelgrote logistieke dienstverlener
- Gespecialiseerd in e-fulfilment en opslag & distributie voor webshops
- **Probleem**: Gebrek aan synergie tussen systemen
- **Doel**: IT-landschap optimaliseren

---

## Slide 3: Huidige Situatie - Problemen
**Operationele Uitdagingen:**
- **Handmatige dubbele invoer** tussen ERP en WMS
- **Geen directe koppelingen** tussen systemen
- **Handmatig kopiëren** van trackingnummers
- **Lange responstijden** bij klantvragen
- **Veel fouten** door handmatige processen

---

## Slide 4: Applicatielandschap - Huidige Staat
**Vier Losstaande Systemen:**

1. **WMS (Warehouse Management System)**
   - On-premise, oudste systeem
   - Beperkte integratiemogelijkheden
   - Kostbare koppelingen

2. **ERP (Enterprise Resource Planning)**
   - Cloud-based
   - Financiën, facturatie, klanten
   - Bedoeld als 'bron van waarheid'

3. **TMS (Transport Management System)**
   - Standalone softwarepakket
   - Verzendlabels en vervoerder communicatie
   - Handmatige data-uitwisseling

4. **CRM (Customer Relationship Management)**
   - Losstaand systeem
   - Alleen klantcontact en klachten
   - Geen automatische synchronisatie

---

## Slide 5: Business Processen - Orderverwerking
**Orderverwerking Workflow:**
1. **Order ontvangen** (API/E-mail/Batch)
2. **Handmatig invoeren** in ERP én WMS
3. **Klantgegevens controleren** (ERP)
4. **Voorraad valideren** (WMS)
5. **Handmatig schakelen** tussen systemen

**Gevolgen:**
- Veel fouten
- Vertraging
- Inefficiëntie

---

## Slide 6: Business Processen - Magazijnbeheer
**Magazijnbeheer Workflow:**
1. **Picking & Packing** (WMS genereert picklijst)
2. **Verzendlabel handmatig maken** (TMS)
3. **Tracking handmatig kopiëren** (TMS → ERP)
4. **Geen directe koppeling** picking ↔ verzending

**Gevolgen:**
- Handmatige zoekacties
- Geen real-time status
- Vertraagde facturatie

---

## Slide 7: Business Processen - Klantenservice
**Klantenservice Workflow:**
1. **Status opzoeken** in ERP én TMS
2. **Handmatig samenvoegen** van informatie
3. **Lange responstijden**
4. **Geen geïntegreerde klanthistorie**

**Gevolgen:**
- Slechte klantervaring
- Inefficiënte medewerkers
- Geen overzicht

---

## Slide 8: ArchiMate Model - Overzicht
**Gemodelleerde Lagen:**
- **Business Laag**: Actoren, Rollen, Processen, Services
- **Applicatie Laag**: Systemen, Services, Data
- **Technologie Laag**: Servers, Netwerk, Devices

**Belangrijkste Elementen:**
- 5 Business Actoren (Grote klant, Kleine klant, Medewerker, Klantenservice, Vervoerder)
- 3 Business Rollen (Orderverwerker, Magazijnmedewerker, Klantenservice medewerker)
- 2 Hoofdprocessen (Orderverwerking, Magazijnbeheer)
- 4 Applicatie Componenten (ERP, WMS, TMS, CRM)

---

## Slide 9: ArchiMate Model - Business Laag
**Business Actoren & Rollen:**
- **Grote klant** → Orders via API
- **Kleine klant** → Orders via e-mail/batch
- **Medewerker** → Orderverwerker & Magazijnmedewerker rollen
- **Klantenservice** → Klantenservice medewerker rol
- **Vervoerder** → Verzendlabel & tracking processen

**Business Services:**
- E-fulfilment dienstverlening
- Opslag & Distributie

---

## Slide 10: ArchiMate Model - Applicatie Laag
**Applicatie Componenten:**
- **ERP Systeem (cloud)**: API, E-mail, Batch, Klantbeheer services
- **WMS (on-premise)**: Voorraad, Picklijst services
- **TMS (standalone)**: Verzendlabel, Tracking services
- **CRM (losstaand)**: Klantcontact service

**Data Objecten:**
- Orderdata, Klantdata, Voorraaddata
- Verzenddata, Trackingdata, Klantcontactdata

---

## Slide 11: ArchiMate Model - Technologie Laag
**Infrastructuur:**
- **Cloud platform** → ERP hosting
- **On-premise server** → WMS hosting
- **TMS Server** → TMS hosting
- **CRM Server** → CRM hosting
- **Netwerk** → Connectiviteit tussen alle systemen

**Devices:**
- **Werkstation medewerker** → Orderverwerking & Klantenservice
- **Magazijn terminal** → Magazijnprocessen

---

## Slide 12: Belangrijkste Bevindingen
**Architectuur Problemen:**
1. **Silo-architectuur**: Geen integratie tussen systemen
2. **Handmatige processen**: Veel foutgevoelige handmatige stappen
3. **Data inconsistentie**: Dubbele invoer leidt tot fouten
4. **Geen real-time informatie**: Vertraagde updates tussen systemen
5. **Inefficiënte workflows**: Medewerkers moeten constant schakelen

---

## Slide 13: Aanbevelingen
**Optimalisatie Mogelijkheden:**
1. **API-integraties** tussen ERP, WMS, TMS en CRM
2. **Geautomatiseerde data-synchronisatie**
3. **Single Sign-On (SSO)** voor medewerkers
4. **Real-time dashboards** voor status overzicht
5. **Workflow automatisering** voor standaard processen

**Prioriteit:**
- Eerst ERP ↔ WMS integratie
- Daarna TMS koppeling
- Tot slot CRM integratie

---

## Slide 14: Conclusie
**ArchiMate Model Voordelen:**
- **Duidelijk overzicht** van huidige architectuur
- **Identificatie van knelpunten** en inefficiënties
- **Basis voor optimalisatie** en integratiestrategie
- **Communicatiemiddel** tussen business en IT

**Volgende Stappen:**
1. Goedkeuring optimalisatieplan
2. Technische haalbaarheidsanalyse
3. Implementatie roadmap
4. Change management strategie

---

## Slide 15: Vragen & Discussie
**Contact:**
- ArchiMate model beschikbaar in Archi tool
- Technische documentatie bijgevoegd
- Implementatie ondersteuning beschikbaar

**Discussiepunten:**
- Prioritering van integraties
- Budget en timeline
- Change management aanpak
- Technische implementatie details
